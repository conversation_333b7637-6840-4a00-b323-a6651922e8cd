# الخطوة 1: تثبيت التبعيات
npm install

# الخطوة 2: تشغيل الموقع
npm start# الخطوة 1: تثبيت التبعيات
npm install

# الخطوة 2: تشغيل الموقع
npm start<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="لوحة تحكم عربية متطورة"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- خطوط عربية من Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <title>لوحة التحكم العربية</title>
  </head>
  <body class="font-arabic">
    <noscript>يجب تفعيل الجافا سكريبت لتشغيل هذا التطبيق.</noscript>
    <div id="root"></div>
  </body>
</html>
