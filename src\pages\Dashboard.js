import React, { useState, useEffect } from 'react';
import { 
  Upload, 
  FileText, 
  Users, 
  BarChart3, 
  TrendingUp,
  Download,
  Eye,
  Trash2,
  Plus,
  Search,
  Filter,
  Calendar,
  Clock
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  const [files, setFiles] = useState([]);
  const [stats, setStats] = useState({
    totalFiles: 0,
    totalSize: 0,
    uploadsToday: 0,
    storageUsed: 45
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // بيانات وهمية للملفات
  useEffect(() => {
    const mockFiles = [
      {
        id: 1,
        name: 'تقرير المبيعات.pdf',
        type: 'pdf',
        size: '2.5 MB',
        uploadDate: '2024-01-15',
        downloads: 12
      },
      {
        id: 2,
        name: 'عرض تقديمي.pptx',
        type: 'presentation',
        size: '8.1 MB',
        uploadDate: '2024-01-14',
        downloads: 5
      },
      {
        id: 3,
        name: 'صورة المنتج.jpg',
        type: 'image',
        size: '1.2 MB',
        uploadDate: '2024-01-13',
        downloads: 23
      }
    ];
    setFiles(mockFiles);
    setStats({
      totalFiles: mockFiles.length,
      totalSize: 11.8,
      uploadsToday: 2,
      storageUsed: 45
    });
  }, []);

  const getFileIcon = (type) => {
    switch (type) {
      case 'pdf':
        return '📄';
      case 'image':
        return '🖼️';
      case 'presentation':
        return '📊';
      default:
        return '📁';
    }
  };

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || file.type === filterType;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-6">
      {/* ترحيب */}
      <div className="bg-gradient-to-l from-primary-600 to-primary-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          مرحباً بك، {user?.name}! 👋
        </h1>
        <p className="opacity-90">
          إليك نظرة سريعة على نشاطك اليوم
        </p>
      </div>

      {/* الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الملفات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalFiles}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <FileText size={24} className="text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">حجم البيانات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalSize} MB</p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <BarChart3 size={24} className="text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">رفع اليوم</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.uploadsToday}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Upload size={24} className="text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">مساحة مستخدمة</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.storageUsed}%</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
              <TrendingUp size={24} className="text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* أدوات سريعة */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          أدوات سريعة
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center space-x-3 space-x-reverse p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors">
            <Upload size={24} className="text-primary-600 dark:text-primary-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">رفع ملف جديد</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">إضافة ملفات إلى مكتبتك</p>
            </div>
          </button>

          <button className="flex items-center space-x-3 space-x-reverse p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
            <FileText size={24} className="text-green-600 dark:text-green-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">إنشاء مجلد</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">تنظيم ملفاتك</p>
            </div>
          </button>

          <button className="flex items-center space-x-3 space-x-reverse p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
            <BarChart3 size={24} className="text-purple-600 dark:text-purple-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">عرض التقارير</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">إحصائيات مفصلة</p>
            </div>
          </button>
        </div>
      </div>

      {/* الملفات الحديثة */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            الملفات الحديثة
          </h2>
          <button className="btn-primary flex items-center space-x-2 space-x-reverse">
            <Plus size={18} />
            <span>رفع ملف</span>
          </button>
        </div>

        {/* أدوات البحث والفلترة */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search size={20} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في الملفات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pr-10"
            />
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="input-field w-full sm:w-auto"
          >
            <option value="all">جميع الأنواع</option>
            <option value="pdf">PDF</option>
            <option value="image">صور</option>
            <option value="presentation">عروض تقديمية</option>
          </select>
        </div>

        {/* قائمة الملفات */}
        <div className="space-y-3">
          {filteredFiles.length > 0 ? (
            filteredFiles.map((file) => (
              <div key={file.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <span className="text-2xl">{getFileIcon(file.type)}</span>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{file.name}</p>
                    <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                      <span>{file.size}</span>
                      <span className="flex items-center space-x-1 space-x-reverse">
                        <Calendar size={14} />
                        <span>{file.uploadDate}</span>
                      </span>
                      <span className="flex items-center space-x-1 space-x-reverse">
                        <Download size={14} />
                        <span>{file.downloads} تحميل</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    <Eye size={18} />
                  </button>
                  <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                    <Download size={18} />
                  </button>
                  <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors">
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <FileText size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 dark:text-gray-400">لا توجد ملفات تطابق البحث</p>
            </div>
          )}
        </div>
      </div>

      {/* النشاط الأخير */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          النشاط الأخير
        </h2>
        <div className="space-y-3">
          {[
            { action: 'رفع ملف', file: 'تقرير المبيعات.pdf', time: 'منذ ساعتين' },
            { action: 'تحميل ملف', file: 'صورة المنتج.jpg', time: 'منذ 4 ساعات' },
            { action: 'حذف ملف', file: 'ملف قديم.doc', time: 'أمس' }
          ].map((activity, index) => (
            <div key={index} className="flex items-center space-x-3 space-x-reverse p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <Clock size={16} className="text-gray-400" />
              <div className="flex-1">
                <p className="text-sm text-gray-900 dark:text-white">
                  <span className="font-medium">{activity.action}</span> - {activity.file}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
