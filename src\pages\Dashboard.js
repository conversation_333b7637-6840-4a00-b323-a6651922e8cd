import React, { useState, useEffect } from 'react';
import {
  Users,
  ShoppingCart,
  Package,
  BarChart3,
  TrendingUp,
  DollarSign,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Settings,
  Activity
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [products, setProducts] = useState([]);
  const [orders, setOrders] = useState([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalProducts: 0,
    totalOrders: 0,
    totalRevenue: 0,
    monthlyGrowth: 12.5,
    activeUsers: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // بيانات وهمية للإدارة
  useEffect(() => {
    const mockUsers = [
      {
        id: 1,
        name: 'أحمد محمد',
        email: '<EMAIL>',
        role: 'مستخدم',
        status: 'نشط',
        joinDate: '2024-01-15',
        lastLogin: 'منذ ساعتين'
      },
      {
        id: 2,
        name: 'فاطمة علي',
        email: '<EMAIL>',
        role: 'مدير',
        status: 'نشط',
        joinDate: '2024-01-10',
        lastLogin: 'منذ 5 دقائق'
      },
      {
        id: 3,
        name: 'محمد حسن',
        email: '<EMAIL>',
        role: 'مستخدم',
        status: 'غير نشط',
        joinDate: '2024-01-08',
        lastLogin: 'منذ يومين'
      }
    ];

    const mockProducts = [
      {
        id: 1,
        name: 'لابتوب Dell XPS',
        category: 'إلكترونيات',
        price: 1200,
        stock: 15,
        status: 'متوفر'
      },
      {
        id: 2,
        name: 'هاتف iPhone 15',
        category: 'إلكترونيات',
        price: 999,
        stock: 8,
        status: 'متوفر'
      },
      {
        id: 3,
        name: 'كتاب البرمجة',
        category: 'كتب',
        price: 25,
        stock: 0,
        status: 'نفد المخزون'
      }
    ];

    const mockOrders = [
      {
        id: 1,
        customer: 'أحمد محمد',
        product: 'لابتوب Dell XPS',
        amount: 1200,
        status: 'مكتمل',
        date: '2024-01-15'
      },
      {
        id: 2,
        customer: 'فاطمة علي',
        product: 'هاتف iPhone 15',
        amount: 999,
        status: 'قيد المعالجة',
        date: '2024-01-14'
      }
    ];

    setUsers(mockUsers);
    setProducts(mockProducts);
    setOrders(mockOrders);
    setStats({
      totalUsers: mockUsers.length,
      totalProducts: mockProducts.length,
      totalOrders: mockOrders.length,
      totalRevenue: mockOrders.reduce((sum, order) => sum + order.amount, 0),
      monthlyGrowth: 12.5,
      activeUsers: mockUsers.filter(user => user.status === 'نشط').length
    });
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'نشط':
      case 'متوفر':
      case 'مكتمل':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      case 'غير نشط':
      case 'نفد المخزون':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'قيد المعالجة':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || user.role === filterType;
    return matchesSearch && matchesFilter;
  });

  // وظائف التفاعل
  const handleUserManagement = () => {
    alert('سيتم فتح صفحة إدارة المستخدمين قريباً!');
  };

  const handleProductManagement = () => {
    alert('سيتم فتح صفحة إدارة المنتجات قريباً!');
  };

  const handleReports = () => {
    alert('سيتم فتح صفحة التقارير والإحصائيات قريباً!');
  };

  const handleSettings = () => {
    alert('سيتم فتح صفحة إعدادات النظام قريباً!');
  };

  const handleAddUser = () => {
    const name = prompt('اسم المستخدم الجديد:');
    const email = prompt('البريد الإلكتروني:');
    if (name && email) {
      const newUser = {
        id: Date.now(),
        name,
        email,
        role: 'مستخدم',
        status: 'نشط',
        joinDate: new Date().toISOString().split('T')[0],
        lastLogin: 'الآن'
      };
      setUsers([...users, newUser]);
      alert('تم إضافة المستخدم بنجاح!');
    }
  };

  const handleViewUser = (user) => {
    alert(`عرض تفاصيل المستخدم:\nالاسم: ${user.name}\nالبريد: ${user.email}\nالدور: ${user.role}\nالحالة: ${user.status}`);
  };

  const handleEditUser = (user) => {
    const newName = prompt('الاسم الجديد:', user.name);
    if (newName && newName !== user.name) {
      const updatedUsers = users.map(u =>
        u.id === user.id ? { ...u, name: newName } : u
      );
      setUsers(updatedUsers);
      alert('تم تحديث المستخدم بنجاح!');
    }
  };

  const handleDeleteUser = (user) => {
    if (window.confirm(`هل أنت متأكد من حذف المستخدم: ${user.name}؟`)) {
      const updatedUsers = users.filter(u => u.id !== user.id);
      setUsers(updatedUsers);
      alert('تم حذف المستخدم بنجاح!');
    }
  };

  return (
    <div className="space-y-6">
      {/* ترحيب */}
      <div className="bg-gradient-to-l from-primary-600 to-primary-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          مرحباً بك، {user?.name}! 👋
        </h1>
        <p className="opacity-90">
          لوحة التحكم الإدارية - نظرة شاملة على النظام
        </p>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي المستخدمين</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalUsers}</p>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-1">
                <TrendingUp size={12} className="ml-1" />
                +{stats.activeUsers} نشط
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <Users size={24} className="text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي المنتجات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalProducts}</p>
              <p className="text-xs text-blue-600 dark:text-blue-400 flex items-center mt-1">
                <Package size={12} className="ml-1" />
                في المخزون
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <Package size={24} className="text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalOrders}</p>
              <p className="text-xs text-purple-600 dark:text-purple-400 flex items-center mt-1">
                <Activity size={12} className="ml-1" />
                هذا الشهر
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <ShoppingCart size={24} className="text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الإيرادات</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">${stats.totalRevenue.toLocaleString()}</p>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-1">
                <TrendingUp size={12} className="ml-1" />
                +{stats.monthlyGrowth}%
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
              <DollarSign size={24} className="text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* أدوات إدارية سريعة */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          أدوات إدارية سريعة
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button
            onClick={handleUserManagement}
            className="flex items-center space-x-3 space-x-reverse p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors"
          >
            <Users size={24} className="text-primary-600 dark:text-primary-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">إدارة المستخدمين</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">إضافة وتعديل المستخدمين</p>
            </div>
          </button>

          <button
            onClick={handleProductManagement}
            className="flex items-center space-x-3 space-x-reverse p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
          >
            <Package size={24} className="text-green-600 dark:text-green-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">إدارة المنتجات</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">إضافة منتجات جديدة</p>
            </div>
          </button>

          <button
            onClick={handleReports}
            className="flex items-center space-x-3 space-x-reverse p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
          >
            <BarChart3 size={24} className="text-purple-600 dark:text-purple-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">التقارير والإحصائيات</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">تحليلات مفصلة</p>
            </div>
          </button>

          <button
            onClick={handleSettings}
            className="flex items-center space-x-3 space-x-reverse p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors"
          >
            <Settings size={24} className="text-orange-600 dark:text-orange-400" />
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">إعدادات النظام</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">تكوين النظام</p>
            </div>
          </button>
        </div>
      </div>

      {/* إدارة المستخدمين */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            إدارة المستخدمين
          </h2>
          <button
            onClick={handleAddUser}
            className="btn-primary flex items-center space-x-2 space-x-reverse"
          >
            <Plus size={18} />
            <span>إضافة مستخدم</span>
          </button>
        </div>

        {/* أدوات البحث والفلترة */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search size={20} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في المستخدمين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pr-10"
            />
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="input-field w-full sm:w-auto"
          >
            <option value="all">جميع الأدوار</option>
            <option value="مدير">مدير</option>
            <option value="مستخدم">مستخدم</option>
          </select>
        </div>

        {/* جدول المستخدمين */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الدور
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  تاريخ الانضمام
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  آخر دخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                          <span className="text-primary-600 dark:text-primary-400 font-medium">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {user.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 dark:text-white">{user.role}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(user.status)}`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {user.joinDate}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {user.lastLogin}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleViewUser(user)}
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                          title="عرض التفاصيل"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          title="تعديل"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteUser(user)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="حذف"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center">
                    <div className="text-gray-500 dark:text-gray-400">
                      <Users size={48} className="mx-auto mb-4 opacity-50" />
                      <p>لا توجد مستخدمين يطابقون البحث</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* الطلبات الأخيرة والمنتجات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الطلبات الأخيرة */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            الطلبات الأخيرة
          </h2>
          <div className="space-y-3">
            {orders.map((order) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {order.customer}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {order.product}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {order.date}
                  </p>
                </div>
                <div className="text-left">
                  <p className="text-sm font-bold text-gray-900 dark:text-white">
                    ${order.amount}
                  </p>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                    {order.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* المنتجات */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            المنتجات
          </h2>
          <div className="space-y-3">
            {products.map((product) => (
              <div key={product.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {product.name}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {product.category}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    المخزون: {product.stock}
                  </p>
                </div>
                <div className="text-left">
                  <p className="text-sm font-bold text-gray-900 dark:text-white">
                    ${product.price}
                  </p>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(product.status)}`}>
                    {product.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
