# تعليمات التثبيت والتشغيل 🚀

## الخطوة 1: تثبيت Node.js
تأكد من تثبيت Node.js (الإصدار 16 أو أحدث) من الموقع الرسمي:
https://nodejs.org/

## الخطوة 2: تثبيت التبعيات
افتح Terminal/Command Prompt في مجلد المشروع وقم بتشغيل:

```bash
npm install
```

## الخطوة 3: تشغيل المشروع
```bash
npm run dev
```
أو
```bash
npm start
```

## الخطوة 4: فتح المتصفح
انتقل إلى: http://localhost:3000

## الأوامر المتاحة:

- `npm start` - تشغيل المشروع في وضع التطوير
- `npm run build` - بناء المشروع للإنتاج
- `npm test` - تشغيل الاختبارات
- `npm run eject` - إخراج إعدادات React (غير مستحسن)

## ملاحظات مهمة:

1. **الخادم (Backend)**: حالياً الواجهة تعمل مع بيانات وهمية. سيتم إضافة الخادم في الخطوة التالية.

2. **المتصفحات المدعومة**: 
   - Chrome (الأحدث)
   - Firefox (الأحدث) 
   - Safari (الأحدث)
   - Edge (الأحدث)

3. **المنافذ**: 
   - الواجهة: http://localhost:3000
   - الخادم (قريباً): http://localhost:5000

## استكشاف الأخطاء:

### خطأ في تثبيت التبعيات:
```bash
npm cache clean --force
rm -rf node_modules
npm install
```

### خطأ في المنفذ:
إذا كان المنفذ 3000 مستخدم، سيتم اقتراح منفذ آخر تلقائياً.

### مشاكل في الخطوط:
تأكد من اتصالك بالإنترنت لتحميل خطوط Google Fonts.

## الخطوات التالية:

بعد تشغيل الواجهة بنجاح، ستحتاج إلى:
1. إعداد الخادم (Backend)
2. إعداد قاعدة البيانات
3. ربط الواجهة مع الخادم

---

**إذا واجهت أي مشاكل، يرجى التواصل معنا!** 💬
