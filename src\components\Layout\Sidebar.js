import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Upload, 
  FileText, 
  Settings, 
  User,
  BarChart3,
  Folder,
  Download
} from 'lucide-react';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      name: 'لوحة التحكم',
      path: '/dashboard',
      icon: Home
    },
    {
      name: 'رفع الملفات',
      path: '/dashboard/upload',
      icon: Upload
    },
    {
      name: 'ملفاتي',
      path: '/dashboard/files',
      icon: Folder
    },
    {
      name: 'التقارير',
      path: '/dashboard/reports',
      icon: BarChart3
    },
    {
      name: 'الملف الشخصي',
      path: '/dashboard/profile',
      icon: User
    },
    {
      name: 'الإعدادات',
      path: '/dashboard/settings',
      icon: Settings
    }
  ];

  return (
    <aside className="fixed right-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 shadow-lg z-40">
      <div className="p-4">
        <nav className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-3 space-x-reverse p-3 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 border-r-4 border-primary-600'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Icon size={20} />
                <span className="font-medium">{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </div>

      {/* معلومات إضافية في أسفل الشريط الجانبي */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            مساحة التخزين
          </h4>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
            <div className="bg-primary-600 h-2 rounded-full" style={{ width: '45%' }}></div>
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            4.5 جيجا من 10 جيجا
          </p>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
