import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth يجب استخدامه داخل AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(localStorage.getItem('token'));

  const checkAuth = async () => {
    try {
      // محاكاة التحقق من المصادقة
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        setUser(JSON.parse(savedUser));
      }
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  // إعداد axios للتوكن
  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      // التحقق من صحة التوكن
      checkAuth();
    } else {
      setLoading(false);
    }
  }, [token, checkAuth]);



  const login = async (email, password) => {
    try {
      // محاكاة تسجيل الدخول
      await new Promise(resolve => setTimeout(resolve, 1000)); // محاكاة تأخير الشبكة

      // التحقق من بيانات الاعتماد (للتجربة فقط)
      if (email === '<EMAIL>' && password === 'admin123') {
        const userData = {
          id: 1,
          name: 'المدير العام',
          email: email,
          role: 'admin'
        };

        const fakeToken = 'fake-jwt-token-' + Date.now();

        localStorage.setItem('token', fakeToken);
        localStorage.setItem('user', JSON.stringify(userData));
        setToken(fakeToken);
        setUser(userData);

        return { success: true };
      } else {
        return {
          success: false,
          message: 'بيانات الاعتماد غير صحيحة. استخدم <EMAIL> / admin123'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'خطأ في تسجيل الدخول'
      };
    }
  };

  const register = async (name, email, password) => {
    try {
      // محاكاة التسجيل
      await new Promise(resolve => setTimeout(resolve, 1000)); // محاكاة تأخير الشبكة

      // التحقق من أن البريد الإلكتروني غير مستخدم (محاكاة)
      const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
      if (existingUsers.find(user => user.email === email)) {
        return {
          success: false,
          message: 'البريد الإلكتروني مستخدم بالفعل'
        };
      }

      const userData = {
        id: Date.now(),
        name: name,
        email: email,
        role: 'user'
      };

      const fakeToken = 'fake-jwt-token-' + Date.now();

      // حفظ المستخدم الجديد
      existingUsers.push(userData);
      localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));

      localStorage.setItem('token', fakeToken);
      localStorage.setItem('user', JSON.stringify(userData));
      setToken(fakeToken);
      setUser(userData);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        message: 'خطأ في التسجيل'
      };
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
    delete axios.defaults.headers.common['Authorization'];
  };

  const value = {
    user,
    login,
    register,
    logout,
    loading,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
