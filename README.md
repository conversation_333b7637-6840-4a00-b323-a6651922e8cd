# لوحة التحكم العربية 🚀

موقع إلكتروني كامل باللغة العربية مع تصميم عصري وآمن، يدعم RTL ويحتوي على نظام إدارة ملفات متطور.

## المميزات ✨

- 🌙 **دعم الوضع الليلي** - تبديل سلس بين الوضع النهاري والليلي
- 🔒 **أمان عالي** - حماية JWT وتشفير كلمات المرور
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🌐 **دعم كامل للعربية** - واجهة RTL مع خطوط عربية جميلة
- 📁 **نظام رفع ملفات** - رفع وإدارة الملفات بأمان
- 👤 **إدارة المستخدمين** - تسجيل دخول وإنشاء حسابات
- 📊 **لوحة تحكم تفاعلية** - إحصائيات ومعلومات مفصلة

## التقنيات المستخدمة 🛠️

### الواجهة الأمامية (Frontend)
- **React.js 18** - مكتبة JavaScript للواجهات
- **TailwindCSS** - إطار عمل CSS للتصميم
- **React Router** - للتنقل بين الصفحات
- **Lucide React** - مكتبة الأيقونات
- **Axios** - للتواصل مع الخادم

### الواجهة الخلفية (Backend) - قريباً
- **Express.js** - إطار عمل Node.js
- **MongoDB/PostgreSQL** - قاعدة البيانات
- **JWT** - للمصادقة والحماية
- **Multer** - لرفع الملفات
- **Bcrypt** - لتشفير كلمات المرور

## التثبيت والتشغيل 🚀

### المتطلبات الأساسية
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd arabic-dashboard
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل الواجهة الأمامية
```bash
npm run dev
# أو
npm start
```

سيتم تشغيل التطبيق على: `http://localhost:3000`

### 4. تشغيل الخادم (Backend) - قريباً
```bash
cd backend
npm install
npm start
# أو للتطوير
nodemon index.js
```

سيتم تشغيل الخادم على: `http://localhost:5000`

## بنية المشروع 📁

```
arabic-dashboard/
├── public/
│   ├── index.html          # الصفحة الرئيسية
│   └── ...
├── src/
│   ├── components/         # المكونات القابلة لإعادة الاستخدام
│   │   ├── Layout/        # مكونات التخطيط
│   │   │   ├── Header.js  # الهيدر
│   │   │   ├── Sidebar.js # الشريط الجانبي
│   │   │   └── Layout.js  # التخطيط الرئيسي
│   │   └── ProtectedRoute.js # حماية الصفحات
│   ├── contexts/          # السياقات (Contexts)
│   │   ├── AuthContext.js # سياق المصادقة
│   │   └── ThemeContext.js # سياق الثيم
│   ├── pages/             # الصفحات
│   │   ├── HomePage.js    # الصفحة الرئيسية
│   │   ├── LoginPage.js   # صفحة تسجيل الدخول
│   │   ├── RegisterPage.js # صفحة التسجيل
│   │   └── Dashboard.js   # لوحة التحكم
│   ├── utils/             # الوظائف المساعدة
│   ├── assets/            # الصور والملفات الثابتة
│   ├── App.js             # المكون الرئيسي
│   ├── index.js           # نقطة الدخول
│   └── index.css          # الأنماط الرئيسية
├── package.json           # تبعيات المشروع
├── tailwind.config.js     # إعدادات TailwindCSS
└── README.md              # هذا الملف
```

## الصفحات المتاحة 📄

- **/** - الصفحة الرئيسية
- **/login** - تسجيل الدخول
- **/register** - إنشاء حساب جديد
- **/dashboard** - لوحة التحكم (محمية)

## المميزات المتقدمة 🎯

### الوضع المظلم
- تبديل سلس بين الوضع النهاري والليلي
- حفظ الاختيار في localStorage
- دعم كامل لجميع المكونات

### التصميم المتجاوب
- يعمل على الهواتف والأجهزة اللوحية وأجهزة الكمبيوتر
- قوائم تنقل متكيفة
- تخطيط مرن

### الأمان
- حماية الصفحات بـ JWT
- تشفير كلمات المرور
- التحقق من صحة البيانات

## التطوير المستقبلي 🔮

- [ ] إضافة الخادم (Backend)
- [ ] نظام رفع الملفات
- [ ] قاعدة البيانات
- [ ] نظام الصلاحيات
- [ ] التقارير والإحصائيات
- [ ] النسخ الاحتياطية
- [ ] إشعارات فورية
- [ ] البحث المتقدم

## المساهمة 🤝

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التغييرات
4. إرسال Pull Request

## الترخيص 📝

هذا المشروع مرخص تحت رخصة MIT.

## الدعم 💬

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue جديد.

---

**تم التطوير بـ ❤️ للمجتمع العربي**
