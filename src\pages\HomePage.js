import React from 'react';
import { Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Shield, 
  Zap, 
  Globe, 
  Upload,
  Users,
  BarChart3,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const HomePage = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: Shield,
      title: 'أمان عالي',
      description: 'حماية متقدمة لبياناتك مع تشفير من الدرجة العسكرية'
    },
    {
      icon: Zap,
      title: 'سرعة فائقة',
      description: 'أداء محسّن وسرعة تحميل استثنائية'
    },
    {
      icon: Globe,
      title: 'دعم كامل للعربية',
      description: 'واجهة مصممة خصيصاً للمستخدمين العرب'
    },
    {
      icon: Upload,
      title: 'رفع ملفات متقدم',
      description: 'رفع وإدارة الملفات بسهولة وأمان'
    }
  ];

  const stats = [
    { number: '10,000+', label: 'مستخدم نشط' },
    { number: '50,000+', label: 'ملف مرفوع' },
    { number: '99.9%', label: 'وقت التشغيل' },
    { number: '24/7', label: 'دعم فني' }
  ];

  return (
    <div className="animate-fade-in">
      {/* القسم الرئيسي */}
      <section className="text-center py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            مرحباً بك في
            <span className="text-primary-600 block mt-2">لوحة التحكم العربية</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto">
            منصة متطورة وآمنة لإدارة ملفاتك وبياناتك بتصميم عربي أنيق وسهل الاستخدام
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {!isAuthenticated ? (
              <>
                <Link
                  to="/register"
                  className="btn-primary flex items-center space-x-2 space-x-reverse text-lg px-8 py-3"
                >
                  <span>ابدأ الآن مجاناً</span>
                  <ArrowLeft size={20} />
                </Link>
                <Link
                  to="/login"
                  className="btn-secondary text-lg px-8 py-3"
                >
                  تسجيل الدخول
                </Link>
              </>
            ) : (
              <Link
                to="/dashboard"
                className="btn-primary flex items-center space-x-2 space-x-reverse text-lg px-8 py-3"
              >
                <span>انتقل إلى لوحة التحكم</span>
                <ArrowLeft size={20} />
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* الإحصائيات */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 dark:text-gray-400">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* المميزات */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              لماذا تختار منصتنا؟
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              نقدم لك أفضل الحلول التقنية مع تجربة مستخدم استثنائية
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="card text-center hover:shadow-lg transition-shadow">
                  <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={32} className="text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* دعوة للعمل */}
      <section className="py-20 bg-primary-600 text-white">
        <div className="max-w-4xl mx-auto text-center px-4">
          <h2 className="text-4xl font-bold mb-4">
            جاهز للبدء؟
          </h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى آلاف المستخدمين الذين يثقون بمنصتنا
          </p>
          
          {!isAuthenticated && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors flex items-center justify-center space-x-2 space-x-reverse"
              >
                <span>إنشاء حساب جديد</span>
                <CheckCircle size={20} />
              </Link>
              <Link
                to="/login"
                className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors"
              >
                لديك حساب بالفعل؟
              </Link>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default HomePage;
