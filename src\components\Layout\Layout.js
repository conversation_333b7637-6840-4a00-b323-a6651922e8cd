import React from 'react';
import { useLocation } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import { useAuth } from '../../contexts/AuthContext';

const Layout = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  
  // إخفاء الشريط الجانبي في صفحات تسجيل الدخول والتسجيل
  const hideSidebar = ['/login', '/register'].includes(location.pathname) || !isAuthenticated;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      <div className="flex">
        {!hideSidebar && <Sidebar />}
        <main className={`flex-1 transition-all duration-300 ${!hideSidebar ? 'mr-64' : ''}`}>
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
