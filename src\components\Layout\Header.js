import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Moon, 
  Sun, 
  User, 
  LogOut, 
  Settings, 
  Menu,
  X,
  Home,
  UserPlus,
  LogIn
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';

const Header = () => {
  const { isDark, toggleTheme } = useTheme();
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
    setShowUserMenu(false);
  };

  return (
    <header className="bg-white dark:bg-gray-800 shadow-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          
          {/* الشعار والعنوان */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3 space-x-reverse">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">ل</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                لوحة التحكم العربية
              </span>
            </Link>
          </div>

          {/* القائمة الرئيسية - سطح المكتب */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <Link 
              to="/" 
              className="flex items-center space-x-2 space-x-reverse text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            >
              <Home size={18} />
              <span>الرئيسية</span>
            </Link>
            
            {!isAuthenticated ? (
              <>
                <Link 
                  to="/login" 
                  className="flex items-center space-x-2 space-x-reverse text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  <LogIn size={18} />
                  <span>تسجيل الدخول</span>
                </Link>
                <Link 
                  to="/register" 
                  className="flex items-center space-x-2 space-x-reverse bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <UserPlus size={18} />
                  <span>تسجيل جديد</span>
                </Link>
              </>
            ) : (
              <Link
                to={user?.role === 'admin' ? '/dashboard' : '/user-dashboard'}
                className="flex items-center space-x-2 space-x-reverse text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
              >
                <Settings size={18} />
                <span>{user?.role === 'admin' ? 'لوحة التحكم' : 'حسابي'}</span>
              </Link>
            )}
          </nav>

          {/* أدوات الهيدر */}
          <div className="flex items-center space-x-4 space-x-reverse">
            
            {/* زر تبديل الوضع المظلم */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              title={isDark ? 'الوضع النهاري' : 'الوضع الليلي'}
            >
              {isDark ? <Sun size={20} /> : <Moon size={20} />}
            </button>

            {/* قائمة المستخدم */}
            {isAuthenticated && (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 space-x-reverse p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <User size={20} />
                  <span className="hidden sm:block">{user?.name}</span>
                </button>

                {showUserMenu && (
                  <div className="dropdown">
                    <div className="py-1">
                      <Link
                        to="/dashboard"
                        className="dropdown-item"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Settings size={16} className="ml-2" />
                        لوحة التحكم
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="dropdown-item w-full text-right flex items-center text-red-600 dark:text-red-400"
                      >
                        <LogOut size={16} className="ml-2" />
                        تسجيل الخروج
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* زر القائمة المحمولة */}
            <button
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              className="md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              {showMobileMenu ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>
        </div>

        {/* القائمة المحمولة */}
        {showMobileMenu && (
          <div className="md:hidden border-t border-gray-200 dark:border-gray-700 py-4">
            <nav className="flex flex-col space-y-2">
              <Link 
                to="/" 
                className="flex items-center space-x-2 space-x-reverse p-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                onClick={() => setShowMobileMenu(false)}
              >
                <Home size={18} />
                <span>الرئيسية</span>
              </Link>
              
              {!isAuthenticated ? (
                <>
                  <Link 
                    to="/login" 
                    className="flex items-center space-x-2 space-x-reverse p-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <LogIn size={18} />
                    <span>تسجيل الدخول</span>
                  </Link>
                  <Link 
                    to="/register" 
                    className="flex items-center space-x-2 space-x-reverse p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <UserPlus size={18} />
                    <span>تسجيل جديد</span>
                  </Link>
                </>
              ) : (
                <Link 
                  to="/dashboard" 
                  className="flex items-center space-x-2 space-x-reverse p-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  onClick={() => setShowMobileMenu(false)}
                >
                  <Settings size={18} />
                  <span>لوحة التحكم</span>
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
